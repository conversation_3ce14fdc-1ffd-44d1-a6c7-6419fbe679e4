from django.contrib import admin
from django.utils.html import format_html
from admin_extra_buttons.api import ExtraButtonsMixin, button

from .models import Language, Powerpoint, AIScripts
from .tasks import create_presentation


@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Language model.

    Configures the admin interface for managing Language objects with customized
    display, filtering, searching, and ordering options.
    """

    list_display = ("name", "code", "created", "modified")
    search_fields = ("name", "code")
    list_filter = ("created", "modified")
    ordering = ("name",)


class PowerpointInline(admin.TabularInline):
    """
    Inline admin configuration for Powerpoint objects.

    Allows for displaying and managing Powerpoint objects directly within
    a parent model's admin page using a tabular format.
    """

    model = Powerpoint
    extra = 0
    fields = ("file", "created", "modified", "view_powerpoint")
    readonly_fields = ("created", "modified", "view_powerpoint")

    def view_powerpoint(self, obj):
        """
        Generates an HTML link to view the PowerPoint file.

        Args:
            obj (Powerpoint): The Powerpoint instance to generate a view link for.

        Returns:
            str: HTML formatted link to the PowerPoint file or a dash if no file exists.
        """
        if obj.file:
            return format_html('<a href="{}" target="_blank">View</a>', obj.file.url)
        return "-"

    view_powerpoint.short_description = "View PowerPoint"


@admin.register(Powerpoint)
class PowerpointAdmin(admin.ModelAdmin):
    """
    Admin configuration for the Powerpoint model.

    Configures the admin interface for managing Powerpoint objects with
    customized display and filtering options.
    """

    list_display = ("id", "file_link", "created", "modified")
    list_filter = ("created", "modified")

    def file_link(self, obj):
        """
        Generates an HTML link to the PowerPoint file with the filename as the link text.

        Args:
            obj (Powerpoint): The Powerpoint instance to generate a file link for.

        Returns:
            str: HTML formatted link to the PowerPoint file with filename as text,
                 or a dash if no file exists.
        """
        if obj.file:
            return format_html(
                '<a href="{}" target="_blank">{}</a>', obj.file.url, obj.file.name
            )
        return "-"

    file_link.short_description = "File"


@admin.register(AIScripts)
class AIScriptsAdmin(ExtraButtonsMixin, admin.ModelAdmin):
    """
    Admin configuration for the AIScripts model.

    Extends the standard ModelAdmin with ExtraButtonsMixin to provide additional
    functionality like custom action buttons. Configures the admin interface for
    managing AIScripts objects with customized display, filtering, searching options,
    and includes PowerpointInline for related Powerpoint objects.
    """

    list_display = (
        "id",
        "grammar_point",
        "target_language",
        "user_language",
        "created",
        "modified",
        "has_script",
    )
    list_filter = ("created", "modified", "target_language", "user_language")
    search_fields = ("grammar_point",)
    raw_id_fields = ("target_language", "user_language")
    readonly_fields = ("script_preview",)
    inlines = [PowerpointInline]

    def has_script(self, obj):
        """
        Checks if the AIScripts object has a script.

        Args:
            obj (AIScripts): The AIScripts instance to check.

        Returns:
            bool: True if the script field is not empty, False otherwise.
        """
        return bool(obj.script)

    has_script.boolean = True
    has_script.short_description = "Has Script"

    def script_preview(self, obj):
        """
        Generates an HTML preview of the script JSON content.

        Args:
            obj (AIScripts): The AIScripts instance to generate a preview for.

        Returns:
            str: HTML formatted preview of the script JSON content (first 200 characters),
                 or a dash if no script exists.
        """
        if obj.script:
            # Display first 200 characters of the script JSON (or less if it's shorter)
            script_str = str(obj.script)
            preview = script_str[:200] + "..." if len(script_str) > 200 else script_str
            return format_html("<pre>{}</pre>", preview)
        return "-"

    script_preview.short_description = "Script Preview"

    @button()
    def create_powerpoint(self, request, pk):
        """
        Custom admin button action to create a PowerPoint presentation.

        Triggers an asynchronous task to create a presentation for the selected
        AIScripts object.

        Args:
            request (HttpRequest): The current request object.
            pk (int): The primary key of the AIScripts object.
        """
        create_presentation.defer(script_id=pk)
