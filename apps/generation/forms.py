from django import forms
from .models import AIScripts


class AIScriptForm(forms.ModelForm):
    """
    Form for creating a new AIScript.

    This form allows users to select target and user languages and specify a grammar point
    to generate a PowerPoint presentation.
    """

    class Meta:
        model = AIScripts
        fields = ["target_language", "user_language", "grammar_point"]
        widgets = {
            "target_language": forms.Select(
                attrs={
                    "class": "select select-bordered w-full",
                    "placeholder": "Select target language",
                }
            ),
            "user_language": forms.Select(
                attrs={
                    "class": "select select-bordered w-full",
                    "placeholder": "Select your language",
                }
            ),
            "grammar_point": forms.TextInput(
                attrs={
                    "class": "input input-bordered w-full",
                    "placeholder": 'Enter grammar point (e.g., "Past tense", "Conditional forms")',
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop("user", None)
        super().__init__(*args, **kwargs)

        # Add help text
        self.fields["target_language"].help_text = "The language you want to teach"
        self.fields["user_language"].help_text = "Your native language for translations"
        self.fields[
            "grammar_point"
        ].help_text = "The specific grammar point to focus on"

    def save(self, commit=True):
        instance = super().save(commit=False)
        if self.user:
            instance.user = self.user
        if commit:
            instance.save()
        return instance
