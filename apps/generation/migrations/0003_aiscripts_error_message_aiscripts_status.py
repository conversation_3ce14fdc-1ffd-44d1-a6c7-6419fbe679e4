# Generated by Django 5.2.1 on 2025-05-19 05:12

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("generation", "0002_aiscripts_user"),
    ]

    operations = [
        migrations.AddField(
            model_name="aiscripts",
            name="error_message",
            field=models.TextField(
                blank=True, help_text="Error message if the process failed", null=True
            ),
        ),
        migrations.AddField(
            model_name="aiscripts",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("generating_script", "Generating Script"),
                    ("script_generated", "Script Generated"),
                    ("creating_presentation", "Creating Presentation"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                ],
                default="pending",
                help_text="Current status of the script and presentation generation process",
                max_length=50,
            ),
        ),
    ]
