import logging
import os
import mimetypes
import tempfile
import uuid
import json

import auto_prefetch

from google import genai
from google.genai import types
from django_lifecycle import LifecycleModel, hook, AFTER_CREATE
from django.conf import settings
from django.db import models
from django.core.files.base import ContentFile
from django.urls import reverse

from model_utils.models import TimeStampedModel
from pptx import Presentation
from pptx.enum.text import PP_ALIGN
from pptx.util import Pt, Inches
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE

from apps.main.models import Notification

from .tasks import generate_script

logger = logging.getLogger("generation")


class Language(auto_prefetch.Model, TimeStampedModel):
    """
    Model that stores the languages that are supported by the AI models
    """

    name = models.CharField(max_length=255)
    code = models.CharField(max_length=255)

    def __str__(self):
        return self.name


class Powerpoint(auto_prefetch.Model, TimeStampedModel, LifecycleModel):
    """
    Model that stores the powerpoints that are created by the AI models
    """

    ai_script = auto_prefetch.ForeignKey(
        "generation.AIScripts",
        on_delete=models.CASCADE,
        related_name="powerpoints",
    )
    file = models.FileField(upload_to="powerpoints/")

    def get_absolute_url(self):
        """
        Returns the URL to the detail view of this PowerPoint presentation.

        Returns:
            str: URL to the detail view.
        """
        return reverse("generation:powerpoint_detail", kwargs={"pk": self.pk})

    def create_from_script(self, slide_data, grammar_point):
        """
        Creates a PowerPoint presentation from the provided script data.

        Args:
            slide_data (list): The validated script data list.
            grammar_point (str): The grammar point being taught.

        Returns:
            Powerpoint: Self, for method chaining.

        Raises:
            ValueError: If script data is invalid.
            Exception: If presentation creation fails.
        """
        logger.info("Creating presentation for script ID: %s", self.ai_script.pk)

        # 1. Generate Presentation Object
        try:
            prs = self._generate_presentation_object(slide_data, grammar_point)
            filename_base = grammar_point.replace(" ", "_").replace("/", "_")
            logger.info("Successfully generated pptx presentation object")
        except Exception as e:
            error_msg = "Error generating presentation object: %s"
            logger.error(error_msg, e, exc_info=True)
            raise

        # 2. Save Presentation Object to Model
        try:
            self._save_presentation_to_file(prs, filename_base)
            logger.info("Successfully saved Powerpoint file")

        except Exception as e:
            error_msg = "Critical error saving presentation file: %s"
            logger.error(error_msg, e, exc_info=True)
            raise

        # 3. Send notification
        Notification.objects.create(
            user=self.ai_script.user,
            title="New Presentation Created",
            message=f"Your presentation for '{grammar_point}' is ready to view and download.",
            link=reverse("generation:powerpoint_list"),
            type="success",
        )

    def _save_presentation_to_file(self, prs_object, filename_base):
        """
        Saves a python-pptx Presentation object to a temporary file and then
        saves the file content to this Powerpoint model instance.

        Args:
            prs_object (pptx.Presentation): The presentation object to save.
            filename_base (str): A base string for the output filename (e.g., grammar point).

        Raises:
            Exception: If any error occurs during saving.
        """

        temp_file_path = None  # Initialize to None

        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pptx") as temp_file:
                temp_file_path = temp_file.name

            logger.debug(
                "Saving presentation object to temporary file: %s", temp_file_path
            )
            prs_object.save(temp_file_path)
            logger.debug("Presentation saved to temporary file.")

            # Create a unique filename for the PowerPoint
            ai_script_id = self.ai_script.pk or uuid.uuid4().hex[:8]
            filename = f"{filename_base}_{ai_script_id}.pptx"
            # Sanitize filename_base in case it contains invalid characters
            filename = "".join(x for x in filename if x.isalnum() or x in "_.- ")
            filename = filename.replace(" ", "_")  # Replace spaces

            # Open the temp file and save it to the Powerpoint model
            logger.debug(
                "Reading temp file and saving to Powerpoint model field: %s", filename
            )
            with open(temp_file_path, "rb") as f:
                self.file.save(filename, ContentFile(f.read()), save=True)
            logger.debug("File content saved to Powerpoint model instance.")

        except Exception as e:
            error_msg = "Error saving presentation file: %s"
            logger.error(error_msg, e, exc_info=True)
            raise

        finally:
            # Ensure the temporary file is deleted
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    logger.debug("Temporary file deleted: %s", temp_file_path)
                except Exception as e:
                    logger.warning(
                        "Failed to delete temporary file %s: %s",
                        temp_file_path,
                        e,
                        exc_info=True,
                    )

    def get_image_for_slide(self, slide_index):  # pylint: disable=too-many-locals
        """
        Generates and retrieves an appropriate image for the given slide index using Google Gemini.

        Args:
            slide_index (int): The index of the slide (0-based).

        Returns:
            str: Path to the generated image file or None if generation fails.
        """

        logger.debug("Generating image for slide %s", slide_index)

        try:
            # Get slide data to create a relevant prompt
            slide_data = self.ai_script.script

            # Handle both direct list and dict with 'slides' key
            if isinstance(slide_data, dict) and "slides" in slide_data:
                slides_list = slide_data["slides"]
            elif isinstance(slide_data, list):
                slides_list = slide_data
            else:
                logger.error("Invalid script data format for image generation")
                return None

            # Ensure slide_index is valid
            if slide_index >= len(slides_list):
                logger.error("Slide index %s is out of range", slide_index)
                return None

            # Extract text content from the slide
            slide_content = slides_list[slide_index]
            translation_text = slide_content.get("translation", "")
            grammar_point = self.ai_script.grammar_point

            # Craft a prompt for the image generation
            image_prompt = f"""
            In pixar style create an educational illustration for a language learning slide that depicts the following scene:

            Scene description: {translation_text}

            This is for teaching the grammar point "{grammar_point}".

            Make the image colorful, clear, and suitable for educational purposes.
            Do not include any text in the image.
            Make the art style in pixar style.
            """

            # Initialize Gemini client
            client = genai.Client(
                api_key=settings.GEMINI_API_KEY,
            )

            # Model and configuration for image generation
            model = "gemini-2.0-flash-preview-image-generation"
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=image_prompt),
                    ],
                ),
            ]

            generate_content_config = types.GenerateContentConfig(
                response_modalities=[
                    "IMAGE",
                    "TEXT",
                ],
                response_mime_type="text/plain",
            )

            # Generate image
            image_data = None
            image_mime_type = None

            for chunk in client.models.generate_content_stream(
                model=model,
                contents=contents,
                config=generate_content_config,
            ):
                if (
                    chunk.candidates is None
                    or chunk.candidates[0].content is None
                    or chunk.candidates[0].content.parts is None
                ):
                    continue

                if chunk.candidates[0].content.parts[0].inline_data:
                    inline_data = chunk.candidates[0].content.parts[0].inline_data
                    image_data = inline_data.data
                    image_mime_type = inline_data.mime_type
                    break

            # If we got image data, save it to a temporary file
            if image_data and image_mime_type:
                file_extension = mimetypes.guess_extension(image_mime_type) or ".png"

                # Create a temporary directory if it doesn't exist
                temp_dir = os.path.join(settings.MEDIA_ROOT, "temp_images")
                os.makedirs(temp_dir, exist_ok=True)

                # Generate a unique filename
                unique_id = uuid.uuid4().hex
                file_name = f"slide_{slide_index}_{unique_id}{file_extension}"
                file_path = os.path.join(temp_dir, file_name)

                # Save the image
                with open(file_path, "wb") as f:
                    f.write(image_data)

                logger.info("Generated image saved to: %s", file_path)
                return file_path

            logger.warning("No image data received for slide %s", slide_index)
            return None

        except Exception as e:
            logger.error(
                "Error generating image for slide %s: %s", slide_index, e, exc_info=True
            )
            return None

    def _generate_presentation_object(self, slide_data, grammar_point):
        """
        Generates a python-pptx Presentation object from a list of slide data.

        Args:
            slide_data (list): A list of dictionaries, each representing a slide.
                            Expected format: [{"target_language": "...", "translation": "..."}, ...]
            grammar_point (str): The grammar point being taught.

        Returns:
            pptx.Presentation: The generated presentation object.

        Raises:
            Exception: If any error occurs during presentation generation.
        """
        prs = Presentation()
        # Create title slide
        self._add_title_slide(prs, grammar_point)
        # Create content slides
        self._add_content_slides(prs, slide_data)

        logger.info("Finished generating all slides.")
        return prs

    def _add_title_slide(self, prs, grammar_point):
        """
        Adds a title slide to the presentation.

        Args:
            prs (pptx.Presentation): The presentation object.
            grammar_point (str): The grammar point being taught.

        Raises:
            Exception: If adding or formatting the title slide fails.
        """
        title_slide_layout = prs.slide_layouts[0]  # Title slide layout
        logger.debug("Adding title slide with grammar point: %s", grammar_point)

        try:
            slide = prs.slides.add_slide(title_slide_layout)
            title_shape = slide.shapes.title
            body_shape = slide.placeholders[
                1
            ]  # Assuming placeholder[1] is the body/subtitle area

            # Format title
            title_shape.text = f"Grammar Point: {grammar_point}"
            title_shape.text_frame.paragraphs[0].font.size = Pt(44)
            title_shape.text_frame.paragraphs[0].font.bold = True

            # Center align the title
            if title_shape.text_frame.paragraphs:
                title_shape.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER

            # Add some introductory text or model info
            body_shape.text = "AI Generated Language Learning Script"
            body_shape.text_frame.paragraphs[0].font.size = Pt(24)
            if body_shape.text_frame.paragraphs:
                body_shape.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER

            self._add_footer_text(slide)

        except Exception as e:
            error_msg = "Could not add/format title slide: %s"
            logger.error(error_msg, e, exc_info=True)
            raise

    def _add_content_slides(self, prs, slide_data):
        """
        Adds content slides to the presentation.

        Args:
            prs (pptx.Presentation): The presentation object.
            slide_data (list): List of slide content dictionaries.

        Raises:
            Exception: If processing any slide fails.
        """
        blank_slide_layout = prs.slide_layouts[6]  # Blank layout for custom content
        logger.debug("Adding %s content slides.", len(slide_data))

        for i, slide_data_item in enumerate(slide_data):
            try:
                slide = prs.slides.add_slide(blank_slide_layout)

                # Add slide elements
                self._add_slide_title(slide, i)
                self._add_image_or_placeholder(slide, i)
                self._add_text_content(slide, slide_data_item)

                self._add_footer_text(slide)

                logger.debug("Added content slide %s/%s", i + 1, len(slide_data))

            except Exception as e:
                error_msg = "Error processing slide %s: %s"
                logger.error(error_msg, i + 1, e, exc_info=True)
                raise

    def _add_slide_title(self, slide, slide_index):
        """
        Adds a title to a content slide.

        Args:
            slide (pptx.slide.Slide): The slide object.
            slide_index (int): The slide index (0-based).
        """
        title_left = Inches(0.5)
        title_top = Inches(0.5)
        title_width = Inches(9.0)
        title_height = Inches(0.8)

        title_shape = slide.shapes.add_textbox(
            title_left, title_top, title_width, title_height
        )
        title_frame = title_shape.text_frame
        title_para = title_frame.add_paragraph()
        title_para.text = f"Slide {slide_index + 1}"
        title_para.font.size = Pt(32)
        title_para.font.bold = True

    def _add_image_or_placeholder(self, slide, slide_index):
        """
        Adds an image or placeholder to a slide.

        Args:
            slide (pptx.slide.Slide): The slide object.
            slide_index (int): The slide index (0-based).
        """
        img_left = Inches(0.5)
        img_top = Inches(1.5)
        img_width = Inches(4.5)
        img_height = Inches(5.0)

        # Try to get an image for this slide
        image_path = self.get_image_for_slide(slide_index)

        if image_path and os.path.exists(image_path):
            # If we have a valid image, add it
            slide.shapes.add_picture(
                image_path,
                img_left,
                img_top,
                width=img_width,
                height=img_height,
            )
        else:
            self._add_image_placeholder(slide, img_left, img_top, img_width, img_height)

    def _add_image_placeholder(self, slide, left, top, width, height):  # pylint: disable=too-many-arguments, too-many-positional-arguments
        """
        Adds an image placeholder to a slide.

        Args:
            slide (pptx.slide.Slide): The slide object.
            left (Inches): Left position.
            top (Inches): Top position.
            width (Inches): Width of the placeholder.
            height (Inches): Height of the placeholder.
        """
        # Add an image placeholder shape
        img_placeholder = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE, left, top, width, height
        )
        # Style the placeholder
        fill = img_placeholder.fill
        fill.solid()
        fill.fore_color.rgb = RGBColor(0xE0, 0xE0, 0xE0)  # Light gray

        # Add text to the placeholder
        txbox = img_placeholder.text_frame
        txbox.text = "Image Placeholder"
        txbox.paragraphs[0].alignment = PP_ALIGN.CENTER
        txbox.paragraphs[0].font.size = Pt(14)
        txbox.paragraphs[0].font.color.rgb = RGBColor(0x80, 0x80, 0x80)  # Dark gray

    def _add_text_content(self, slide, slide_data_item):
        """
        Adds text content to a slide.

        Args:
            slide (pptx.slide.Slide): The slide object.
            slide_data_item (dict): Dictionary containing the slide content.
        """
        text_left = Inches(5.5)
        text_top = Inches(1.5)
        text_width = Inches(4.5)
        text_height = Inches(5.0)

        textbox = slide.shapes.add_textbox(text_left, text_top, text_width, text_height)
        text_frame = textbox.text_frame
        text_frame.word_wrap = True

        # Add primary content and translation
        self._add_primary_content(
            text_frame, slide_data_item.get("target_language", "")
        )
        self._add_translation(text_frame, slide_data_item.get("translation", ""))

    def _add_primary_content(self, text_frame, target_language_text):
        """
        Adds the primary (target language) content to a text frame.

        Args:
            text_frame (pptx.text.text.TextFrame): The text frame.
            target_language_text (str): The target language text.
        """
        p_primary = text_frame.add_paragraph()

        # Split the text by ** markers to handle formatting
        parts = target_language_text.split("**")
        for j, part in enumerate(parts):
            run = p_primary.add_run()
            run.text = part

            # Every odd index (1, 3, 5...) should be emphasized (bold and different color)
            if j % 2 == 1:
                run.font.bold = True
                # Use a vibrant color for grammar highlights
                grammar_highlight_color = RGBColor(0xFF, 0x45, 0x00)  # Orange-Red
                run.font.color.rgb = grammar_highlight_color

        # Apply font size to the entire primary content paragraph
        p_primary.font.size = Pt(24)
        p_primary.space_after = Pt(12)  # Add space after target language text

    def _add_translation(self, text_frame, translation_text):
        """
        Adds the translation text to a text frame.

        Args:
            text_frame (pptx.text.text.TextFrame): The text frame.
            translation_text (str): The translation text.
        """
        p_translation = text_frame.add_paragraph()
        p_translation.text = translation_text
        p_translation.font.size = Pt(18)
        p_translation.font.italic = True
        grey_color = RGBColor(0x64, 0x64, 0x64)  # Hex for 100, 100, 100
        p_translation.font.color.rgb = grey_color

    def _add_footer_text(self, slide):
        """
        Adds a footer text with branding to a slide.

        Args:
            slide (pptx.slide.Slide): The slide object.
        """
        footer_left = Inches(0.5)
        footer_top = Inches(7.0)  # Position near the bottom of the slide
        footer_width = Inches(9.0)
        footer_height = Inches(0.3)

        footer_shape = slide.shapes.add_textbox(
            footer_left, footer_top, footer_width, footer_height
        )
        footer_frame = footer_shape.text_frame
        footer_para = footer_frame.add_paragraph()
        footer_para.text = "Made by Eduslides.pro"
        footer_para.font.size = Pt(12)
        footer_para.font.italic = True
        footer_para.font.color.rgb = RGBColor(0x80, 0x80, 0x80)  # Gray color
        footer_para.alignment = PP_ALIGN.RIGHT  # Right-aligned


class AIScripts(auto_prefetch.Model, TimeStampedModel, LifecycleModel):  # pylint: disable=too-many-ancestors
    """
    Model that stores the AI scripts that are created by the googles Gemini
    """

    class StatusChoices(models.TextChoices):  # pylint: disable=too-many-ancestors
        """
        Choices for the status field.
        """

        PENDING = "pending", "Pending"
        GENERATING_SCRIPT = "generating_script", "Generating Script"
        SCRIPT_GENERATED = "script_generated", "Script Generated"
        CREATING_PRESENTATION = "creating_presentation", "Creating Presentation"
        COMPLETED = "completed", "Completed"
        FAILED = "failed", "Failed"

    user = auto_prefetch.ForeignKey(
        "users.User", on_delete=models.CASCADE, related_name="ai_scripts"
    )
    target_language = auto_prefetch.ForeignKey(
        "generation.Language", on_delete=models.CASCADE, related_name="ai_scripts"
    )
    user_language = auto_prefetch.ForeignKey(
        "generation.Language",
        on_delete=models.CASCADE,
        related_name="ai_scripts_user_language",
    )
    grammar_point = models.CharField(max_length=255)
    script = models.JSONField(blank=True, null=True)
    status = models.CharField(
        max_length=50,
        choices=StatusChoices.choices,
        default=StatusChoices.PENDING,
        help_text="Current status of the script and presentation generation process",
    )
    error_message = models.TextField(
        blank=True, null=True, help_text="Error message if the process failed"
    )

    def generate_script(self):
        """Generate the script from the AI model"""
        # Initialize an empty string to build the complete JSON output from the stream
        full_json_string = ""

        client = genai.Client(
            api_key=settings.GEMINI_API_KEY,
        )

        model = "gemini-2.5-flash-preview-04-17"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_text(
                        text=f"""
                        In json format create a slide show with {self.target_language.name} dialogue that focuses on the grammar {self.grammar_point} it should be 10 slides and the slides should tell a story.
                          The json should be a story where each new slide is a continuation of the story.
                            The json should have content then {self.user_language.name} translation.
                            Each slide should use the grammar point in a unique way.
                    """
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=genai.types.Schema(
                type=genai.types.Type.OBJECT,
                properties={
                    "slides": genai.types.Schema(
                        type=genai.types.Type.ARRAY,
                        items=genai.types.Schema(
                            type=genai.types.Type.OBJECT,
                            properties={
                                "target_language": genai.types.Schema(
                                    type=genai.types.Type.STRING,
                                ),
                                "translation": genai.types.Schema(
                                    type=genai.types.Type.STRING,
                                ),
                            },
                        ),
                    ),
                },
            ),
        )

        try:
            # Stream the response and accumulate the text
            for chunk in client.models.generate_content_stream(
                model=model,
                contents=contents,
                config=generate_content_config,
            ):
                # Append the chunk text to the temporary string
                if chunk.text:  # Ensure chunk.text is not None or empty
                    full_json_string += chunk.text

            # After the loop, parse the accumulated string into a Python object
            if full_json_string:  # Check if any text was received
                self.script = json.loads(full_json_string)
            else:
                # Handle case where no response text was received
                self.script = {"error": "No content received from AI"}

        except json.JSONDecodeError as e:
            # Handle cases where the accumulated text is not valid JSON
            logger.error("Error decoding JSON from AI response: %s", e)
            logger.error("Raw AI response: %s", full_json_string)
            self.script = {
                "error": f"Failed to decode AI response: {e}",
                "raw_output": full_json_string,
            }
        except Exception as e:
            # Handle other potential API errors
            logger.error("Error during AI script generation: %s", e)
            self.script = {"error": f"AI generation failed: {e}"}

        self.save()  # Save the script

    @hook(AFTER_CREATE)
    def generate_script_hook(self):
        """Generate the script from the AI model"""
        generate_script.defer(script_id=self.pk)

    def create_presentation(self):
        """Create the presentation from the script"""
        self._create_presentation()

    def _create_presentation(self):
        """
        Creates a PowerPoint presentation from the stored script JSON and
        saves it as a Powerpoint model instance associated with this script.

        Returns:
            Powerpoint: The created Powerpoint model instance.

        Raises:
            ValueError: If script data is invalid or contains errors.
            Exception: If any step in the presentation creation process fails.
        """

        logger.info("Attempting to create presentation for script ID: %s", self.pk)

        # Get and Validate Script Data
        slide_data = self._get_and_validate_script_data()

        # Handle cases where the script data is invalid or indicates an error
        if slide_data is None:
            error_msg = "Script data invalid or indicates error for ID %s."
            logger.error(error_msg, self.pk)
            raise ValueError(error_msg % self.pk)

        # Create and save the PowerPoint through the Powerpoint model
        try:
            powerpoint = Powerpoint(ai_script=self)
            powerpoint.create_from_script(slide_data, self.grammar_point)
            logger.info(
                "Successfully created Powerpoint model for script ID %s. Powerpoint ID: %s",
                self.pk,
                powerpoint.pk,
            )
            return powerpoint
        except Exception as e:
            error_msg = "Error creating presentation for script ID %s: %s"
            logger.error(error_msg, self.pk, e, exc_info=True)
            raise

    def _get_and_validate_script_data(self):
        """
        Retrieves the script data from self.script and validates its format.
        Logs warnings if the format is unexpected.

        Returns:
            list: The slide data list if valid.

        Raises:
            AssertionError: If the script data format is invalid.
            ValueError: If the script data is None or contains an error.
        """
        script_data = self.script

        # Check if script_data is None
        if script_data is None:
            error_msg = "Script data is None for script ID %s."
            logger.error(error_msg, self.pk)
            raise ValueError(error_msg % self.pk)

        # Check for error in script_data
        if isinstance(script_data, dict) and "error" in script_data:
            error_msg = "Script data for ID %s contains an error: %s"
            logger.error(error_msg, self.pk, script_data["error"])
            raise ValueError(error_msg % (self.pk, script_data["error"]))

        # Extract slides list based on data structure
        slides_list = None

        try:
            # Case 1: Dict with 'slides' key
            if isinstance(script_data, dict) and "slides" in script_data:
                assert isinstance(script_data["slides"], list), (
                    f"'slides' field is not a list (it's {type(script_data['slides'])})"
                )
                slides_list = script_data["slides"]

            # Case 2: Direct list
            elif isinstance(script_data, list):
                slides_list = script_data

            # Invalid format
            else:
                assert False, (
                    f"Script data is neither a list nor a dict with 'slides' key (it's {type(script_data)})"
                )

            # Check if the slides list is empty
            assert slides_list and len(slides_list) > 0, "Slides list is empty"

            # Additional validation: each slide should have target_language and translation
            for i, slide in enumerate(slides_list):
                assert isinstance(slide, dict), f"Slide {i} is not a dictionary"
                assert "target_language" in slide, (
                    f"Slide {i} missing 'target_language' field"
                )
                assert "translation" in slide, f"Slide {i} missing 'translation' field"

            return slides_list

        except AssertionError as e:
            error_msg = "Script data validation failed for ID %s: %s. Content: %s..."
            logger.error(error_msg, self.pk, str(e), str(script_data)[:200])
            raise ValueError(
                error_msg % (self.pk, str(e), str(script_data)[:200])
            ) from e
