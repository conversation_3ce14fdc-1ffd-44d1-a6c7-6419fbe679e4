import logging
from procrastinate.contrib.django import app


logger = logging.getLogger("generation")


@app.task()
def generate_script(script_id: int) -> None:
    """Generate the script from the AI model"""
    from .models import AIScripts  # pylint: disable=import-outside-toplevel

    try:
        script = AIScripts.objects.get(id=script_id)
        script.status = "generating_script"
        script.save(update_fields=["status"])

        script.generate_script()

        script.refresh_from_db()
        script.status = "script_generated"
        script.save(update_fields=["status"])

        # Automatically start creating the presentation
        create_presentation.defer(script_id=script_id)
    except Exception as e:
        logger.exception(
            "Error generating script for ID {script_id}: {str(e)}",
            script_id=script_id,
            e=e,
        )
        try:
            script = AIScripts.objects.get(id=script_id)
            script.status = "failed"
            script.error_message = str(e)
            script.save(update_fields=["status", "error_message"])
        except Exception as inner_e:
            logger.exception(
                "Error updating script status for ID {script_id}: {str(inner_e)}",
                script_id=script_id,
                inner_e=inner_e,
            )


@app.task()
def create_presentation(script_id: int) -> None:
    """Create the presentation from the script"""
    from .models import AIScripts  # pylint: disable=import-outside-toplevel

    try:
        script = AIScripts.objects.get(id=script_id)
        script.status = "creating_presentation"
        script.save(update_fields=["status"])

        script.create_presentation()

        script.refresh_from_db()
        script.status = "completed"
        script.save(update_fields=["status"])
    except Exception as e:
        logger.exception(
            "Error creating presentation for ID {script_id}: {str(e)}",
            script_id=script_id,
            e=e,
        )
        try:
            script = AIScripts.objects.get(id=script_id)
            script.status = "failed"
            script.error_message = str(e)
            script.save(update_fields=["status", "error_message"])
        except Exception as inner_e:
            logger.exception(
                "Error updating script status for ID {script_id}: {str(inner_e)}",
                script_id=script_id,
                inner_e=inner_e,
            )
