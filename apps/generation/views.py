from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required

from .models import Powerpoint
from .forms import AIScriptForm
from .tasks import generate_script


@login_required
def powerpoint_list(request):
    """
    View to display a list of PowerPoint presentations created by the user.

    This view shows all PowerPoint presentations associated with the user's
    AI scripts, ordered by creation date (newest first).
    """
    powerpoints = Powerpoint.objects.filter(ai_script__user=request.user).order_by(
        "-created"
    )
    return render(
        request, "generation/powerpoint_list.html", {"powerpoints": powerpoints}
    )


@login_required
def powerpoint_detail(request, pk):
    """
    View to display details of a specific PowerPoint presentation.

    This view shows the details of a PowerPoint presentation, including
    its file, creation date, and associated AI script.
    """
    powerpoint = get_object_or_404(Powerpoint, pk=pk, ai_script__user=request.user)
    return render(
        request, "generation/powerpoint_detail.html", {"powerpoint": powerpoint}
    )


@login_required
def aiscript_create(request):
    """
    View to create a new AIScript.

    This view allows users to create a new AIScript by selecting languages
    and specifying a grammar point.
    """
    if request.method == "POST":
        if not request.user.has_generation_access:
            return render(
                request,
                "generation/aiscript_create.html",
                {
                    "form": AIScriptForm(user=request.user),
                    "error": "You have no generation access. Upgrade your plan to generate more presentations.",
                },
            )

        form = AIScriptForm(request.POST, user=request.user)
        if form.is_valid():
            # Save the form with the current user
            aiscript = form.save(commit=False)
            aiscript.user = request.user
            aiscript.save()

            # Start the script generation process
            generate_script.defer(script_id=aiscript.pk)

            # Redirect to the PowerPoint list page
            powerpoints = Powerpoint.objects.filter(
                ai_script__user=request.user
            ).order_by("-created")
            return render(
                request, "generation/powerpoint_list.html", {"powerpoints": powerpoints}
            )
    else:
        form = AIScriptForm(user=request.user)

    return render(request, "generation/aiscript_create.html", {"form": form})
