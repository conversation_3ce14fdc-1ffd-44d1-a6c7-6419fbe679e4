from django.contrib import admin
from apps.payments.models import Plan, Purchase

# Register your models here.


@admin.register(Plan)
class PlanAdmin(admin.ModelAdmin):
    """Admin for the Plan model"""

    list_display = ("name", "price")
    search_fields = ("name",)


@admin.register(Purchase)
class PurchaseAdmin(admin.ModelAdmin):
    """Admin for the Purchase model"""

    list_display = ("user", "plan", "price_paid", "is_active")
    list_filter = ("is_active",)
    search_fields = ("user__username", "plan__name")
