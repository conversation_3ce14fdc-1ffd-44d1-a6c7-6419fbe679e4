import stripe
from django_lifecycle import LifecycleModel
from django.conf import settings
from django.db import models
from django.urls import reverse
from model_utils.models import TimeStampedModel

stripe.api_key = settings.STRIPE_API_SK


class Plan(TimeStampedModel):
    """Represent the plans that we sell to users"""

    name = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    feature = models.JSONField()

    def __str__(self):
        return self.name

    def create_transaction(self, request) -> stripe.checkout.Session:
        """Create a transaction for this plan"""
        return stripe.checkout.Session.create(
            payment_method_types=["card"],
            line_items=[
                {
                    "price_data": {
                        "currency": "usd",
                        "unit_amount": int(self.price * 100),  # Stripe requires cents
                        "product_data": {
                            "name": self.name,
                            "description": f"{self.name}",
                        },
                    },
                    "quantity": 1,
                },
            ],
            mode="payment",
            success_url=request.build_absolute_uri(
                reverse("generation:powerpoint_list")
            ),
            cancel_url=request.build_absolute_uri(
                reverse("generation:powerpoint_list")
            ),
        )


class Purchase(TimeStampedModel, LifecycleModel):
    """Represent a purchase of a plan by a user"""

    user = models.ForeignKey(
        "users.User", on_delete=models.CASCADE, related_name="purchases"
    )
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE)
    stripe_payment_intent_id = models.CharField(max_length=100)

    price_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="The price that the user paid. This is stored on save to make sure it doesnt change even if we change the price",
    )

    is_active = models.BooleanField(default=False)

    class Meta:
        """
        Meta class for the model
        We set constraints so a user cant purchase the same plan twice
        """

        constraints = [
            models.UniqueConstraint(
                fields=["user", "plan"], name="unique_purchase_per_user_per_plan"
            )
        ]

    @property
    def status(self):
        """Return the status from Stripe"""
        if not self.payment_intent_id:
            return "No payment intent ID"

        try:
            payment_intent = stripe.PaymentIntent.retrieve(self.payment_intent_id)
            if (
                payment_intent.status == "requires_payment_method"
            ):  # Stripe returns this instead of failed when a payment fails
                return "Failed"

            return payment_intent.status
        except stripe.error.StripeError as e:
            # Handle potential Stripe API errors
            return f"Error retrieving status: {str(e)}"

    def activate(self):
        """Activate the purchase"""
        self.is_active = True
        self.save(update_fields=["is_active"])

    def deactivate(self):
        """Deactivate the purchase"""
        self.is_active = False
        self.save(update_fields=["is_active"])

    def handle_dispute(self, stripe_event):
        """Handle a dispute event"""
        event_data = stripe_event["data"]["object"]

        # If we did not lose the dispute then do nothing
        if not event_data.get("status") == "lost":
            return

        self.deactivate()
