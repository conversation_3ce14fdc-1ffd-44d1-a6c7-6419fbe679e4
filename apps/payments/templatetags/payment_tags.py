from django import template
from django.conf import settings
from apps.payments.models import Plan

register = template.Library()


@register.inclusion_tag("payments/plans.html", takes_context=True)
def render_plans(context):
    """Render the pricing component with data from the database"""
    request = context.get("request")
    is_authenticated = request.user.is_authenticated if request else False

    # Get all plans directly from the database
    plans = Plan.objects.all()

    return {
        "plans": plans,
        "is_authenticated": is_authenticated,
        "stripe_key": settings.STRIPE_API_PK,
    }
