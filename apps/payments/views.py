import logging
import stripe
from django.conf import settings
from django.contrib.auth.decorators import login_not_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from apps.payments.models import Plan, Purchase

stripe.api_key = settings.STRIPE_API_SK

logger = logging.getLogger("payments")


@csrf_exempt
def create_checkout_session(request, plan_id):
    """Create a checkout session for a plan"""
    try:
        plan = Plan.objects.get(id=plan_id)

        # Create a new Checkout Session
        checkout_session = plan.create_transaction(request)

        Purchase.objects.create(
            user=request.user,
            plan=plan,
            stripe_payment_intent_id=checkout_session.id,
            price_paid=plan.price,
        )

        # Make sure we're explicitly returning the ID in the format Stripe expects
        return JsonResponse(
            {
                "sessionId": checkout_session.id  # Changed from 'id' to 'sessionId'
            }
        )
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=400)


@login_not_required
@csrf_exempt
def stripe_webhook(request):
    """Handle a stripe webhook"""
    payload = request.body
    sig_header = request.META["HTTP_STRIPE_SIGNATURE"]
    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
        )
    except ValueError as e:
        logger.error("Invalid payload: {e}", e=e)
        # Invalid payload
        return JsonResponse({"error": "Invalid payload"}, status=400)
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        logger.error("Invalid signature: {e}", e=e)
        return JsonResponse({"error": "Invalid signature"}, status=400)

    payment_intent_id = event["data"]["object"]["payment_intent"]

    purchase = Purchase.objects.get(stripe_payment_intent_id=payment_intent_id)

    if event.type == "payment_intent.succeeded":
        purchase.activate()
    elif event.type == "charge.dispute.closed":
        purchase.handle_dispute(event["data"]["object"])

    return JsonResponse({"status": "success"})
