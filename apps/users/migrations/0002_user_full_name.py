# Generated by Django 5.1.3 on 2024-11-19 07:17

import django.db.models.functions.text
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="full_name",
            field=models.GeneratedField(
                db_persist=True,
                expression=django.db.models.functions.text.Concat(
                    models.F("first_name"),
                    models.Value(" "),
                    models.F("last_name"),
                    output_field=models.CharField(),
                ),
                output_field=models.CharField(max_length=255),
            ),
        ),
    ]
