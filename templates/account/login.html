{% extends "base.html" %}
{% load i18n %}
{% load account socialaccount %}

{% block head_title %}{% trans "Sign In" %}{% endblock %}

{% block content %}
    <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 bg-white rounded-2xl shadow-2xl p-8 transform transition-all duration-500 hover:scale-105">
            <div class="transform transition-all duration-500 hover:-translate-y-1">
                <h2 class="mt-6 text-center text-3xl font-extrabold bg-gradient-to-r from-gray-800 to-black bg-clip-text text-transparent">
                    {% trans "Sign In" %}
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    {% trans "If you have not created an account yet, then please" %}
                    <a href="{% url 'account_signup' %}" class="font-medium text-gray-900 hover:text-gray-700 transition-colors duration-300 underline underline-offset-4">
                        {% trans "sign up" %}
                    </a>
                    {% trans "first." %}
                </p>
            </div>

            <div class="mt-8 space-y-6">
            <!-- Social Login Buttons -->
                <div class="space-y-4">
                    <a href='{% provider_login_url 'google' %}'
                       class="group relative w-full flex justify-center py-3 px-4 border border-gray-200 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-300 hover:shadow-lg">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="w-5 h-5 text-gray-500 group-hover:text-gray-700" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                                    <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z"/>
                                    <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z"/>
                                    <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z"/>
                                    <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z"/>
                                </g>
                            </svg>
                        </span>
                        {% trans "Continue with Google" %}
                    </a>




                </div>

            <!-- Hidden next field -->
                {% if request.GET.next %}
                    <input type="hidden" name="{{ redirect_field_name }}" value="{{ request.GET.next }}">
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}