<!-- hero.html - Django template fragment -->

<!-- Alpine.js Canvas animation code -->
<script>
    // Define the essential classes outside of Alpine
  function Node() {
    this.x = 0;
    this.y = 0;
    this.vy = 0;
    this.vx = 0;
  }

  function Oscillator(config) {
    config = config || {};
    this.phase = config.phase || 0;
    this.offset = config.offset || 0;
    this.frequency = config.frequency || 0.001;
    this.amplitude = config.amplitude || 1;
    this.e = 0;
  }

  Oscillator.prototype.update = function() {
    this.phase += this.frequency;
    this.e = this.offset + Math.sin(this.phase) * this.amplitude;
    return this.e;
  };

  Oscillator.prototype.value = function() {
    return this.e;
  };

  function Line(config, E, pos) {
    config = config || {};
    this.spring = config.spring + 0.1 * Math.random() - 0.05;
    this.friction = E.friction + 0.01 * Math.random() - 0.005;
    this.nodes = [];

    for (let n = 0; n < E.size; n++) {
      let t = new Node();
      t.x = pos.x;
      t.y = pos.y;
      this.nodes.push(t);
    }
  }

  Line.prototype.update = function(pos, E) {
    let e = this.spring;
    let t = this.nodes[0];

    t.vx += (pos.x - t.x) * e;
    t.vy += (pos.y - t.y) * e;

    for (let i = 0, a = this.nodes.length; i < a; i++) {
      t = this.nodes[i];
      if (i > 0) {
        let n = this.nodes[i - 1];
        t.vx += (n.x - t.x) * e;
        t.vy += (n.y - t.y) * e;
        t.vx += n.vx * E.dampening;
        t.vy += n.vy * E.dampening;
      }

      t.vx *= this.friction;
      t.vy *= this.friction;
      t.x += t.vx;
      t.y += t.vy;
      e *= E.tension;
    }
  };

  Line.prototype.draw = function(ctx) {
      // Ensure we have nodes
    if (!this.nodes || this.nodes.length < 2) return;

    let e, t;
    let n = this.nodes[0].x;
    let i = this.nodes[0].y;

    ctx.beginPath();
    ctx.moveTo(n, i);

    const nodeLength = this.nodes.length;
      // Limit the number of nodes to process for performance
    const limit = Math.min(nodeLength - 1, 20); // Process max 20 nodes

    for (let a = 1, o = limit - 1; a < o; a++) {
      e = this.nodes[a];
      t = this.nodes[a + 1];
      if (!e || !t) continue; // Skip if nodes are undefined

      n = 0.5 * (e.x + t.x);
      i = 0.5 * (e.y + t.y);
      ctx.quadraticCurveTo(e.x, e.y, n, i);
    }

      // Get last two nodes for final curve
    e = this.nodes[limit - 1];
    t = this.nodes[limit];
    if (e && t) {
      ctx.quadraticCurveTo(e.x, e.y, t.x, t.y);
    }

    ctx.stroke();
    ctx.closePath();
  };

  document.addEventListener('alpine:init', () => {
    Alpine.data('canvasAnimation', () => ({
      ctx: null,
      f: null,
      e: 0,
      pos: { x: 0, y: 0 },
      lines: [],
      E: {
        debug: true,
        friction: 0.5,
        trails: 30,    // Reduced from 80
        size: 30,      // Reduced from 50
        dampening: 0.025,
        tension: 0.99
      },

      init() {
          // Initialize canvas after page is loaded
        this.$nextTick(() => {
          this.initCanvas();
        });
      },

      initCanvas() {
        const canvas = document.getElementById('canvas');
        if (!canvas) return;

        this.ctx = canvas.getContext('2d');
        this.ctx.running = true;
        this.ctx.frame = 1;

        this.f = new Oscillator({
          phase: Math.random() * 2 * Math.PI,
          amplitude: 85,
          frequency: 0.0015,
          offset: 285
        });

          // Add event listeners
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('touchstart', this.handleMouseMove.bind(this));
        document.body.addEventListener('orientationchange', this.resizeCanvas.bind(this));
        window.addEventListener('resize', this.resizeCanvas.bind(this));

        window.addEventListener('focus', () => {
          if (!this.ctx.running) {
            this.ctx.running = true;
            this.render();
          }
        });

        window.addEventListener('blur', () => {
          this.ctx.running = true;
        });

        this.resizeCanvas();
        this.createLines();
        this.render();
      },

      handleMouseMove(e) {
        if (e.touches) {
          this.pos.x = e.touches[0].pageX;
          this.pos.y = e.touches[0].pageY;
        } else {
          this.pos.x = e.clientX;
          this.pos.y = e.clientY;
        }
        e.preventDefault();
      },

      createLines() {
        this.lines = [];
          // Reduce the number of trails for better performance
        const trailCount = Math.min(this.E.trails, 30); // Limit to 30 trails max
        for (let e = 0; e < trailCount; e++) {
          this.lines.push(new Line({
            spring: 0.45 + (e / trailCount) * 0.025
          }, this.E, this.pos));
        }
      },

      render() {
        if (!this.ctx || !this.ctx.running) return;

        this.ctx.globalCompositeOperation = "source-over";
        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
        this.ctx.globalCompositeOperation = "lighter";
        this.ctx.strokeStyle = "hsla(" + Math.round(this.f.update()) + ",100%,50%,0.025)";
        this.ctx.lineWidth = 5; // Reduced line width for performance

          // Only update every other frame for better performance
        if (this.ctx.frame % 2 === 0) {
          for (let t = 0; t < this.lines.length; t++) {
            const line = this.lines[t];
            line.update(this.pos, this.E);
            line.draw(this.ctx);
          }
        }

        this.ctx.frame++;
        requestAnimationFrame(this.render.bind(this));
      },

      resizeCanvas() {
        const canvas = document.getElementById('canvas');
        if (!canvas) return;

        canvas.width = window.innerWidth - 20;
        canvas.height = window.innerHeight;
      }
    }));
  });
</script>

  <!-- Add custom keyframes for fadeIn animation -->
<style>
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
</style>

  <!-- Canvas is placed first in DOM so it's underneath the content -->
<section id="home" x-data="canvasAnimation" class="relative min-h-screen w-full">
    <!-- Canvas background - visible but behind content -->
  <canvas id="canvas" class="absolute inset-0 mx-auto w-full h-full" style="z-index: 0;"></canvas>

    <!-- Content container with explicit z-index -->
  <div class="relative z-10 mt-20 flex flex-col items-center justify-center px-4 text-center md:mt-20" style="animation: fadeIn 1s ease-in-out 0.8s forwards; opacity: 0;">
      <!-- Top banner -->
    <div class="z-50 mb-6 mt-10 sm:justify-center md:mb-4 md:mt-20">
      <div class="relative flex items-center whitespace-nowrap rounded-full border border-gray-200 bg-white bg-opacity-10 px-3 py-1 text-xs leading-6 text-gray-800">
          <!-- Shape Icon -->
        <svg class="h-5 p-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="8" r="4"></circle>
          <rect x="8" y="14" width="8" height="8" rx="1"></rect>
        </svg>
        <span class="ml-1">Introducing EduSlides.</span>
        <a href="#" rel="noreferrer" class="ml-1 flex items-center font-semibold text-gray-800 hover:text-indigo-500">
          <div class="absolute inset-0 flex" aria-hidden="true"></div>
          <span>Explore</span>
          <span aria-hidden="true">
              <!-- Arrow Right Icon -->
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </span>
        </a>
      </div>
    </div>

      <!-- Main content -->
    <div class="mb-10 mt-4 w-full md:mt-6">
      <div class="px-2">
          <!-- Featured Box -->
        <div class="relative mx-auto h-full max-w-7xl border border-indigo-500 p-6 md:px-12 md:py-20" style="mask-image: radial-gradient(800rem 96rem at center, white, transparent);">
            <!-- Plus Icons -->
          <svg stroke-width="4" class="absolute -left-5 -top-5 h-10 w-10 text-indigo-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M12 5v14M5 12h14"></path>
          </svg>
          <svg stroke-width="4" class="absolute -bottom-5 -left-5 h-10 w-10 text-indigo-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M12 5v14M5 12h14"></path>
          </svg>
          <svg stroke-width="4" class="absolute -right-5 -top-5 h-10 w-10 text-indigo-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M12 5v14M5 12h14"></path>
          </svg>
          <svg stroke-width="4" class="absolute -bottom-5 -right-5 h-10 w-10 text-indigo-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M12 5v14M5 12h14"></path>
          </svg>

            <!-- Heading -->
          <h1 class="flex select-none flex-col px-3 py-2 text-center text-5xl font-semibold leading-none tracking-tight text-gray-900 md:flex-col md:text-8xl lg:flex-row lg:text-8xl">
            Your complete platform for language teaching.
          </h1>

            <!-- Status indicator -->
          <div class="flex items-center justify-center gap-1">
            <span class="relative flex h-3 w-3 items-center justify-center">
              <span class="absolute inline-flex h-full w-full animate-ping rounded-full bg-green-500 opacity-75"></span>
              <span class="relative inline-flex h-2 w-2 rounded-full bg-green-500"></span>
            </span>
            <p class="text-xs text-green-500">Available Now</p>
          </div>
        </div>
      </div>



        <!-- Description -->
      <p class="mx-auto mb-16 mt-2 max-w-2xl px-6 text-sm text-gray-600 sm:px-6 md:max-w-4xl md:px-20 md:text-base lg:text-lg">
        EduSlides is a platform for creating interactive language learning content.
      </p>

        <!-- Call to actions - increased visibility and contrast -->
      <div class="flex justify-center gap-2">
        <a href="{% url 'account_signup' %}" class="inline-flex h-11 items-center justify-center rounded-md bg-indigo-500 px-8 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
          Sign Up
        </a>

      </div>
    </div>
  </div>
</section>