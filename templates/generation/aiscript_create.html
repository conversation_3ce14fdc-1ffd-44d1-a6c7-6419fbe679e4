{% extends 'base.html' %}

{% block title %}Create New Presentation - EduSlides{% endblock %}

{% block content %}
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Create New Presentation</h1>
            <p class="mt-2 text-sm text-gray-600">Generate a PowerPoint presentation for language learning</p>
        </div>

        <div class="bg-white shadow overflow-hidden sm:rounded-lg p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}

                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                    <div class="sm:col-span-1">
                        <label for="{{ form.target_language.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Target Language
                        </label>
                        <div class="mt-1">
                            {{ form.target_language }}
                        </div>
                        {% if form.target_language.help_text %}
                            <p class="mt-2 text-sm text-gray-500">{{ form.target_language.help_text }}</p>
                        {% endif %}
                        {% if form.target_language.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.target_language.errors }}</p>
                        {% endif %}
                    </div>

                    <div class="sm:col-span-1">
                        <label for="{{ form.user_language.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Your Language
                        </label>
                        <div class="mt-1">
                            {{ form.user_language }}
                        </div>
                        {% if form.user_language.help_text %}
                            <p class="mt-2 text-sm text-gray-500">{{ form.user_language.help_text }}</p>
                        {% endif %}
                        {% if form.user_language.errors %}
                            <p class="mt-2 text-sm text-red-600">{{ form.user_language.errors }}</p>
                        {% endif %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.grammar_point.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Grammar Point
                    </label>
                    <div class="mt-1">
                        {{ form.grammar_point }}
                    </div>
                    {% if form.grammar_point.help_text %}
                        <p class="mt-2 text-sm text-gray-500">{{ form.grammar_point.help_text }}</p>
                    {% endif %}
                    {% if form.grammar_point.errors %}
                        <p class="mt-2 text-sm text-red-600">{{ form.grammar_point.errors }}</p>
                    {% endif %}
                </div>

                <div class="pt-5">
                    <div class="flex justify-end">
                        <a href="{% url 'generation:powerpoint_list' %}" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Cancel
                        </a>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Generate Presentation
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
{% endblock %}
