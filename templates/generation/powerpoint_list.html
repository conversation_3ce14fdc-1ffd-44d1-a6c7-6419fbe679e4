{% extends 'base.html' %}

{% block title %}My Presentations - EduSlides{% endblock %}

{% block content %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">My Presentations</h1>
                <p class="mt-2 text-sm text-gray-600">View and download your generated PowerPoint presentations</p>
            </div>
            <div>
                <a href="{% url 'generation:aiscript_create' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Create New Presentation
                </a>
            </div>
        </div>

        {% if powerpoints %}
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <ul class="divide-y divide-gray-200">
                    {% for powerpoint in powerpoints %}
                        <li>
                            <div class="px-4 py-4 sm:px-6 flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-indigo-600">
                                            {{ powerpoint.ai_script.grammar_point }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            Created: {{ powerpoint.created|date:"F j, Y, g:i a" }}
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            Languages: {{ powerpoint.ai_script.target_language.name }} / {{ powerpoint.ai_script.user_language.name }}
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <a href="{% url 'generation:powerpoint_detail' powerpoint.id %}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        View Details
                                    </a>
                                    <a href="{{ powerpoint.file.url }}" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" download>
                                        Download
                                    </a>
                                </div>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            </div>

            {% if page_obj.has_other_pages %}
                {% include 'components/pagination.html' %}
            {% endif %}
        {% else %}
            <div class="bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No presentations found</h3>
                <p class="mt-1 text-sm text-gray-500">You haven't created any presentations yet.</p>
            </div>
        {% endif %}
    </div>
{% endblock %}
