<div
    x-data="{}"
    class="min-h-screen bg-[#f0f0f0] p-4 sm:p-6 lg:p-8 relative overflow-hidden rounded-[12px]"
>
    <!-- Background Effects -->
    <div class="absolute inset-0">
        <div class="absolute w-2 h-2 bg-black/5 rounded-full animate-float" style="left: 20%; top: 30%;"></div>
        <div class="absolute w-2 h-2 bg-black/5 rounded-full animate-float" style="left: 80%; top: 40%; animation-delay: 1s;"></div>
        <div class="absolute w-2 h-2 bg-black/5 rounded-full animate-float" style="left: 40%; top: 70%; animation-delay: 2s;"></div>
    </div>
    <div class="absolute inset-0" style="
                                         background-image: linear-gradient(#00000008 1px, transparent 1px), linear-gradient(90deg, #00000008 1px, transparent 1px);
                                         background-size: 16px 16px;
                                        "></div>

    <!-- Header -->
    <div class="text-center mb-8 sm:mb-12 relative z-10">
        <div class="inline-block animate-fadeIn">
            <h1 class="text-3xl sm:text-4xl lg:text-5xl font-black text-slate-800
                       bg-gradient-to-r from-white to-gray-100 px-8 py-4 rounded-xl border-4 border-black
                       shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9),_15px_15px_15px_-3px_rgba(0,0,0,0.1)]
                       transform transition-transform hover:translate-x-1 hover:translate-y-1 mb-3 relative
                       before:absolute before:inset-0 before:bg-white/50 before:rounded-xl before:blur-sm before:-z-10">
                Pricing Plans
            </h1>
            <div class="h-2 bg-gradient-to-r from-black via-gray-600 to-black rounded-full animate-scaleX"></div>
        </div>
    </div>

    <!-- Pricing Cards -->
    <div class="w-[100%] max-w-5xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 relative z-10">
        {% for plan in plans %}
            <div
                x-data="{
                        mouseX: 0,
                        mouseY: 0,
                        showSignup: false,
                        async checkout() {
                        {% if is_authenticated %}
                            const stripe = Stripe('{{ stripe_key }}');
                            try {
                            // Get CSRF token
                            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                            const response = await fetch('{% url 'create-checkout-session' plan.id %}', {
                            method: 'POST',
                            headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken
                            }
                            });

                            if (!response.ok) {
                            throw new Error('Network response was not ok');
                            }

                            const data = await response.json();
                            console.log('Session data:', data); // Debug output

                            // Check for sessionId
                            if (!data.sessionId) {
                            throw new Error('No session ID returned from server');
                            }

                            // Redirect to Checkout in a modal
                            const result = await stripe.redirectToCheckout({
                            sessionId: data.sessionId
                            });

                            if (result.error) {
                            console.error('Stripe error:', result.error.message);
                            }
                            } catch (error) {
                            console.error('Error:', error);
                            alert('Payment setup failed. Please try again.');
                            }
                        {% else %}
                            window.location.href = '{% url 'account_signup' %}';
                        {% endif %}
                        }
                        }"
                @mousemove="
                            const rect = $el.getBoundingClientRect();
                            const centerX = rect.x + rect.width / 2;
                            const centerY = rect.y + rect.height / 2;
                            mouseX = (event.clientX - centerX) / rect.width;
                            mouseY = (event.clientY - centerY) / rect.height;
                           "
                @mouseleave="mouseX = 0; mouseY = 0; showSignup = false"
                @mouseenter="{% if not is_authenticated %}showSignup = true{% endif %}"
                :style="`
                        transform: perspective(1000px)
                        rotateX(${mouseY * -14}deg)
                        rotateY(${mouseX * 14}deg);
                        transition: transform 0.1s ease;
                        `"
                class="relative w-full bg-white rounded-xl p-6 border-3 border-black
                       shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]
                       hover:shadow-[8px_8px_0px_0px_rgba(0,0,0,0.9)]
                       transition-all duration-200 animate-fadeIn"
                style="animation-delay: {{ forloop.counter0 }}00ms"
            >
            <!-- Price Badge -->
                <div
                    class="absolute -top-4 -right-4 w-16 h-16
                           rounded-full flex items-center justify-center border-2 border-black
                           shadow-[3px_3px_0px_0px_rgba(0,0,0,0.9)] animate-bounce-price
                           {% if forloop.counter == 1 %}bg-blue-500{% elif forloop.counter == 2 %}bg-purple-500{% else %}bg-pink-500{% endif %}"
                >
                    <div class="text-center text-white">
                        <div class="text-lg font-black" x-show="!showSignup">${{ plan.price }}</div>
                    <!-- Remove the "/mo" text since it's a one-time payment -->
                        <div class="text-sm font-bold" x-show="showSignup">Sign Up</div>
                    </div>
                </div>

            <!-- Plan Name and Popular Badge -->
                <div class="mb-4">
                    <h3 class="text-xl font-black text-black mb-2">{{ plan.name }}</h3>
                    {% if plan.is_popular %}
                        <span
                            class="inline-block px-3 py-1 text-white font-bold rounded-md text-xs border-2 border-black
                                   shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] animate-popular
                                   {% if forloop.counter == 1 %}bg-blue-500{% elif forloop.counter == 2 %}bg-purple-500{% else %}bg-pink-500{% endif %}"
                        >
                            POPULAR
                        </span>
                    {% endif %}
                </div>

            <!-- Features List -->
                <div class="space-y-2 mb-4">
                    {% for feature in plan.feature %}
                        <div
                            class="flex items-center gap-2 p-2 bg-gray-50 rounded-md border-2 border-black
                                   shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)] hover:translate-x-1 transition-transform animate-slideIn"
                            style="animation-delay: {{ forloop.counter0 }}0ms"
                        >
                            <span
                                class="{% if forloop.parentloop.counter == 1 %}bg-blue-500{% elif forloop.parentloop.counter == 2 %}bg-purple-500{% else %}bg-pink-500{% endif %}
                                       w-5 h-5 rounded-md flex items-center justify-center
                                       text-white font-bold text-xs border border-black
                                       shadow-[1px_1px_0px_0px_rgba(0,0,0,0.9)] hover:scale-110 hover:rotate-12 transition-all"
                            >
                                ✓
                            </span>
                            <span class="text-black font-bold text-sm">{{ feature }}</span>
                        </div>
                    {% endfor %}
                </div>

            <!-- CTA Button -->
                <button
                    @click="checkout()"
                    class="{% if forloop.counter == 1 %}bg-blue-500{% elif forloop.counter == 2 %}bg-purple-500{% else %}bg-pink-500{% endif %}
                           w-full py-2 rounded-lg text-white font-black text-sm
                           border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]
                           hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]
                           active:shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]
                           hover:scale-[1.02] active:scale-[0.95] transition-all duration-200"
                >
                    {% if is_authenticated %}GET STARTED{% else %}SIGN UP{% endif %} →
                </button>
            </div>
        {% endfor %}
    </div>
</div>
</div>

<style>
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    @keyframes scaleX {
        from { transform: scaleX(0); }
        to { transform: scaleX(1); }
    }
    @keyframes float {
        0% { transform: translateY(0) scale(1); opacity: 0.3; }
        50% { transform: translateY(-20px) scale(1.5); opacity: 0.6; }
        100% { transform: translateY(0) scale(1); opacity: 0.3; }
    }
    @keyframes slideIn {
        from { opacity: 0; transform: translateX(-20px); }
        to { opacity: 1; transform: translateX(0); }
    }
    @keyframes bounceprice {
        0% { transform: rotate(0deg) translateY(0); }
        25% { transform: rotate(5deg) translateY(-3px); }
        50% { transform: rotate(0deg) translateY(0); }
        75% { transform: rotate(-5deg) translateY(3px); }
        100% { transform: rotate(0deg) translateY(0); }
    }
    @keyframes popular {
        0% { transform: translateY(0); }
        50% { transform: translateY(-3px) scale(1.05); }
        100% { transform: translateY(0); }
    }

    .animate-fadeIn { animation: fadeIn 0.5s forwards; }
    .animate-scaleX { animation: scaleX 0.5s 0.5s forwards; }
    .animate-float { animation: float 4s infinite; }
    .animate-slideIn { animation: slideIn 0.3s forwards; }
    .animate-bounce-price { animation: bounceprice 5s infinite; }
    .animate-popular { animation: popular 2s infinite; }
</style>