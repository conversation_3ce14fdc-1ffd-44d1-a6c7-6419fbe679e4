{% extends 'base.html' %}
{% load payment_tags %}

{% block content %}
    <div class="min-h-screen bg-gray-100 py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <!-- Header -->
                <div class="bg-gradient-to-r from-gray-800 to-black px-6 py-8">
                    <div class="flex items-center">
                        <div class="h-20 w-20 rounded-full overflow-hidden border-4 border-white">
                            <img src="{{ user.avatar_url }}" alt="{{ user.username }}" class="h-full w-full object-cover">
                        </div>
                        <div class="ml-6">
                            <h1 class="text-2xl font-bold text-white">{{ user.full_name }}</h1>
                            <p class="text-gray-300">{{ user.email }}</p>
                        </div>
                    </div>
                </div>

            <!-- Content -->
                <div class="px-6 py-8">
                    <h2 class="text-xl font-semibold text-gray-800 mb-6">Account Information</h2>

                <!-- User Info Section -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-8 shadow-sm">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Username</h3>
                                <p class="mt-1 text-lg font-medium text-gray-900">{{ user.username }}</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Email</h3>
                                <p class="mt-1 text-lg font-medium text-gray-900">{{ user.email }}</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">First Name</h3>
                                <p class="mt-1 text-lg font-medium text-gray-900">{{ user.first_name }}</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Last Name</h3>
                                <p class="mt-1 text-lg font-medium text-gray-900">{{ user.last_name }}</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Date Joined</h3>
                                <p class="mt-1 text-lg font-medium text-gray-900">{{ user.date_joined|date:"F j, Y" }}</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Last Login</h3>
                                <p class="mt-1 text-lg font-medium text-gray-900">{{ user.last_login|date:"F j, Y" }}</p>
                            </div>
                        </div>
                    </div>

                <!-- Subscription Section -->
                    <h2 class="text-xl font-semibold text-gray-800 mb-6">Subscription</h2>
                    <div class="bg-gray-50 rounded-lg p-6 shadow-sm">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Current Plan</h3>
                                <p class="mt-1 text-lg font-medium text-gray-900">{{ user.current_plan }}</p>
                            </div>
                            {% if user.current_plan == "Trial" %}
                                <button
                                    x-data=""
                                    x-on:click="$dispatch('open-plans-modal')"
                                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">
                                    Upgrade Plan
                                </button>
                            {% endif %}
                        </div>

                        {% if active_purchase %}
                            <div class="mt-6 border-t border-gray-200 pt-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-500">Plan Name</h3>
                                        <p class="mt-1 text-lg font-medium text-gray-900">{{ active_purchase.plan.name }}</p>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-500">Price Paid</h3>
                                        <p class="mt-1 text-lg font-medium text-gray-900">${{ active_purchase.price_paid }}</p>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-500">Purchase Date</h3>
                                        <p class="mt-1 text-lg font-medium text-gray-900">{{ active_purchase.created|date:"F j, Y" }}</p>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-500">Status</h3>
                                        <p class="mt-1 text-lg font-medium text-gray-900">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% elif user.current_plan == "Trial" %}
                            <div class="mt-6 border-t border-gray-200 pt-6">
                                <p class="text-gray-600">You are currently on the Trial plan. Upgrade to access more features!</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plans Modal -->
    <div
        x-data="{ open: false }"
        x-show="open"
        x-on:keydown.escape.window="open = false"
        x-on:open-plans-modal.window="open = true; $el.classList.remove('hidden')"
        x-on:close-plans-modal.window="open = false; setTimeout(() => { $el.classList.add('hidden') }, 200)"
        id="plans-modal"
        class="fixed inset-0 z-50 overflow-y-auto hidden"
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
        x-transition:enter="ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
    >
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div
                class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                aria-hidden="true"
                x-on:click="$dispatch('close-plans-modal')"
            ></div>

            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full">
                <!-- Close button -->
                <div class="absolute top-0 right-0 pt-4 pr-4 z-10">
                    <button type="button" x-on:click="$dispatch('close-plans-modal')" class="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- Modal content -->
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-2xl leading-6 font-bold text-gray-900 mb-4 text-center" id="modal-title">
                                Choose Your Plan
                            </h3>
                            <div class="mt-2">
                                {% csrf_token %}
                                {% render_plans %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
